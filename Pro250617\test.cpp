
#include <opencv2/opencv.hpp>
#include <iostream>

using namespace cv;
using namespace std;

int main()
{
    VideoCapture cap(0);
    // 检查摄像头是否打开成功
    if (!cap.isOpened()) {
        cerr << "无法打开摄像头！" << endl;
        return -1;
    }

    Mat img;
    bool running = true;

    while (running)
    {
        cap >> img;
        if (img.empty())
        {
            cerr << "无法获取图像！" << endl;
            break;
        }
        namedWindow("img", WINDOW_NORMAL);
        imshow("img", img);

        // 等待按键，支持ESC(27)和'q'键退出
        int key = waitKey(20);
        if (key == 27 || key == 'q')
        {
            running = false;
            cout << "程序已退出" << endl;
        }
    }

    // 释放资源
    cap.release();
    destroyAllWindows();
    return 0;
}

