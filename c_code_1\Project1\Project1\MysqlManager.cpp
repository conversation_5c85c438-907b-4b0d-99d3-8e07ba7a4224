//#include "MysqlManager.h"
//
////官方API
////初始化数据库连接
////bool ContactUtil::dirtyFlag = false;
//bool MysqlManager::init() {
//    mysql_init(m_Mysql); //mysql初始化函数，返回可用的mysql连接句柄
//    if (!mysql_real_connect(m_Mysql, m_host, m_username, m_password, m_database, m_port, m_unix_socket, m_clientflag))//如果连接成功则返回该句柄，否则返回NULL
//    {
//        return false;
//    }
//    if (mysql_set_character_set(m_Mysql, "utf8mb4"))//返回0表示成功，否则返回非零值失败,utf8mb4是utf8的超集，能够保存和表示更多Unicode中的字符
//    {
//        cerr << "字符集配置失败:" << mysql_error(m_Mysql) << endl;//获取报错信息
//        return false;
//    }
//    return true;
//}
////数据库连接的释放
//void MysqlManager::freeConnect() {
//    mysql_free_result(m_SelectResult);//释放存储查询结果集资源（缓冲）
//    mysql_close(m_Mysql);             //释放mysql连接资源
//}
////执行对mysql数据库的增删改操作(insert、delete、update三种sql语句) 
//bool MysqlManager::runSql(const char* sql) {
//    if (mysql_query(m_Mysql, sql))//更新成功返回0，失败返回非零值 
//    {
//        cerr << "更新出错：" << mysql_error(m_Mysql) << endl;
//        return false;
//    }
//    else {
//        cout << "数据库更新操作成功!" << endl;
//    }
//    return true;
//}
//
////执行对mysql数据库的查询操作(select语句)
//bool MysqlManager::selectData(const char* sql) {
//    if (mysql_query(m_Mysql, sql))//失败返回非零值 
//    {
//        cerr << "查询出错：" << mysql_error(m_Mysql) << endl;
//        return false;
//    }
//    else {
//        cout << sql << "查询结果如下：" << endl;
//    }
//    m_SelectResult = mysql_store_result(m_Mysql);
//    return true;
//}
////从数据库加载好友信息到通讯录容器
//map<string, Friend> MysqlManager::loadContact() {
//    const char* sql = "SELECT * FROM friend";
//    selectData(sql);
//    map<string, Friend> contact{};
//    Friend fri{};
//    MYSQL_ROW row;//结果集返回数据库中的一行记录 - row为NULL时，表示已无更多行
//    while (row = mysql_fetch_row(m_SelectResult)) {
//        strcpy(fri.name, row[1]);//数据库内设置的行row[1]为name
//        fri.id = atoi(row[0]);//row[0]为id - 数组,atoi()函数将数组转换为int
//        strcpy(fri.phone, row[2]);//row[2]为phone
//        strcpy(fri.address, row[3]);//row[3]为address
//        contact[fri.phone] = fri;//放入map中
//    }
//    return contact;
//}
////保存好友信息到数据库
//void MysqlManager::saveContact(const map<string, Friend>& contact) {
//    //先清空friend表
//    const char* clearFriendTable = "DELETE FROM friend";
//    if (!runSql(clearFriendTable))//返回0表示失败
//    {
//        cout << "清空好友表失败!" << endl;
//        return;
//    }
//    else {
//        cout << "好友表已被清空！" << endl;
//    }
//
//    //再保存最新的好友信息到数据库friend表中
//    const char* insertFriendSql = "INSERT INTO friend(name,phone,address) values(?,?,?)";
//    MYSQL_STMT* stmt = mysql_stmt_init(m_Mysql);//初始化预处理语句
//    if (!stmt) {
//        cout << "初始化预处理语句失败!" << endl;
//        return;
//    }
//    if (mysql_stmt_prepare(stmt, insertFriendSql, strlen(insertFriendSql)))//返回1表示失败
//    {
//        cout << "预处理语句准备失败:" << mysql_stmt_error(stmt) << endl;
//        mysql_stmt_close(stmt);//关闭预处理语句
//        return;
//    }
//
//    MYSQL_BIND bind[3];
//    memset(bind, 0, sizeof(bind));//清空内存
//    char name[20];//替换?占位符
//    char phone[20];
//    char address[100];
//    unsigned long name_len, phone_len, address_len;//实际长度
//    bind[0].buffer_type = MYSQL_TYPE_STRING;//缓冲类型
//    bind[0].buffer = name;//缓冲名字
//    bind[0].buffer_length = sizeof(name);//缓冲长度
//    bind[0].length = &name_len;//数据实际长度
//    bind[1].buffer_type = MYSQL_TYPE_STRING;
//    bind[1].buffer = phone;
//    bind[1].buffer_length = sizeof(phone);
//    bind[1].length = &phone_len;
//    bind[2].buffer_type = MYSQL_TYPE_STRING;
//    bind[2].buffer = address;
//    bind[2].buffer_length = sizeof(address);
//    bind[2].length = &address_len;
//
//    if (mysql_stmt_bind_param(stmt, bind))//将参数绑定到预处理语句，1表示失败
//    {
//        cout << "绑定参数失败!" << mysql_stmt_error(stmt) << endl;
//        mysql_stmt_close(stmt);
//        return;
//    }
//
//    for (const auto& pair : contact)//保存好友信息
//    {
//        const Friend& fri = pair.second;
//        strncpy(name, fri.name, sizeof(name));//复制到缓冲中
//        name_len = strlen(name);
//        strncpy(phone, fri.phone, sizeof(phone));
//        phone_len = strlen(phone);
//        strncpy(address, fri.address, sizeof(address));
//        address_len = strlen(address);
//        if (mysql_stmt_execute(stmt)) {
//            cout << "预处理语句执行失败：" << mysql_stmt_error(stmt) << endl;
//        }
//        else {
//            cout << "成功插入了好友:" << name << endl;
//        }
//    }
//    mysql_stmt_close(stmt);
//}
//
//
//void pressEnter()
//{
//    cout << "请按回车继续..." << endl;
//    cin.get();
//    system("cls");
//}
//
////void MysqlManager::menu(map<string, Friend>& contact, MysqlManager& mysql) {
////    ContactUtil* util = ContactUtil::getUtil();
////    util->contacts = contact;
////    cout << "好友通信名单" << endl;
////    for (auto pair : util->contacts) {
////        cout << "手机号:" << pair.first << "---姓名:" << pair.second.name << ",地址:" << pair.second.address << endl;
////    }
////    int choice;
////    do {
////        cout << "1.添加好友信息" << endl;
////        cout << "2.删除好友信息" << endl;
////        cout << "3.修改好友信息" << endl;
////        cout << "4.查找好友信息" << endl;
////        cout << "5.退出" << endl;
////        cout << "6.保存" << endl;
////        cout << "7.加载" << endl;
////        cin >> choice;
////        switch (choice) {
////        case 1:
////            util->addContact();
////            break;
////        case 2:
////            util->deleteContact();
////            break;
////        case 3:
////            util->updateContact();
////            break;
////        case 4:
////            util->searchContact();
////            break;
////        case 5:
////            if (util->dirtyFlag) {
////                cout << "是否保存修改？Y/N" << endl;
////                char c;
////                cin >> c;
////                if (c == 'y' || c == 'Y') {
////                    mysql.saveContact(contact);
////                }
////                else {
////                    cout << "已放弃保存" << endl;
////                }
////            }
////            break;
////        case 6:
////            mysql.saveContact(contact);
////            break;
////        case 7:
////            mysql.loadContact();
////            break;
////        default:
////            cout << "输入错误" << endl;
////            break;
////        }
////    } while (choice != 5);
////}
//
//int main() {
//    MysqlManager mysql;
//    mysql.m_host = "127.0.0.1";//localhost
//    mysql.m_username = "root";
//    mysql.m_password = "xx14151611";
//    mysql.m_database = "contact";
//    mysql.m_port = 3306;
//    mysql.m_unix_socket = NULL;
//    mysql.m_clientflag = 0;
//    if (!mysql.init())//连接数据库失败返回false
//    {
//        return -1;
//    }
//    map<string, Friend> contact = mysql.loadContact();//加载好友信息到通讯录容器
//    for (const auto& pair : contact) {
//        cout << "好友信息：" << pair.second.name << "," << pair.second.phone << "," << pair.second.address << endl;
//    }
//
//
//   // mysql.menu(contact, mysql);//调用菜单
//
//    mysql.saveContact(contact);
//    mysql.freeConnect();
//
//    return 0;
//}
//
