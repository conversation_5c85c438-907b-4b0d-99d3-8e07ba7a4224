@echo off
chcp 65001 >nul
echo ========================================
echo LogRich 日志自动清理功能快速验证
echo ========================================
echo.
echo 此脚本将：
echo 1. 启动LogRich主程序
echo 2. 等待用户手动开始监听
echo 3. 运行测试客户端发送1200条数据
echo 4. 验证自动清理功能
echo.
echo 请按以下步骤操作：
echo.

echo 第1步：启动LogRich主程序
echo ----------------------------------------
start "" "bin\Release\net6.0-windows\LogRich.exe"
echo LogRich主程序已启动，请在主程序中：
echo 1. 点击"开始监听"按钮
echo 2. 确认状态显示"正在监听端口 12380"
echo.
pause

echo.
echo 第2步：运行自动清理测试
echo ----------------------------------------
echo 即将发送300条测试数据...
echo 请观察LogRich主程序的日志显示：
echo.
echo 预期行为：
echo - 前200条：日志正常累积
echo - 第200+条：开始自动清理
echo - 显示提示："自动清理日志：保留最新 200 行"
echo - 日志行数保持在200行左右
echo.
pause

echo 开始发送测试数据...
cd LogClearTest
echo 300 | echo 20 | dotnet run --project LogClearTest.csproj

echo.
echo 第3步：验证结果
echo ----------------------------------------
echo 请检查LogRich主程序：
echo.
echo ✓ 检查项目：
echo   1. 日志行数是否控制在200行左右？
echo   2. 是否出现"自动清理日志"提示信息？
echo   3. 界面是否保持流畅响应？
echo   4. 最新的日志信息是否仍然可见？
echo.
echo 如果以上4项都符合预期，则自动清理功能正常！
echo.
pause

echo.
echo ========================================
echo 验证完成！
echo ========================================
echo.
echo 如果发现问题，请查看"日志自动清理测试说明.md"
echo 获取详细的故障排除指南。
echo.
pause
