#include "MysqlManager.h"

//官方API
//初始化数据库连接
bool MysqlManager::init(){
    mysql_init(m_Mysql); //mysql初始化函数，返回可用的mysql连接句柄
    if(!mysql_real_connect(m_Mysql,m_host,m_username,m_password,m_database,m_port,m_unix_socket,m_clientflag))//如果连接成功则返回该句柄，否则返回NULL
    {
        return false;
    }
    if(mysql_set_character_set(m_Mysql,"utf8mb4"))//返回0表示成功，否则返回非零值失败,utf8mb4是utf8的超集，能够保存和表示更多Unicode中的字符
    {
        cerr<<"字符集配置失败:"<<mysql_error(m_Mysql)<<endl;//获取报错信息
        return false;
    }
    return true;
} 
//数据库连接的释放
void MysqlManager::freeConnect(){
    mysql_free_result(m_SelectResult);//释放存储查询结果集资源（缓冲）
    mysql_close(m_Mysql);             //释放mysql连接资源
} 
//执行对mysql数据库的增删改操作(insert、delete、update三种sql语句) 
bool MysqlManager::runSql(const char* sql){
    if(mysql_query(m_Mysql,sql))//更新成功返回0，失败返回非零值 
    {
        cerr<<"更新出错："<<mysql_error(m_Mysql)<<endl;
        return false;
    }else{
        cout<<"数据库更新操作成功!"<<endl;
    }
    return true;
}

//执行对mysql数据库的查询操作(select语句)
bool MysqlManager::selectData(const char* sql){
    if(mysql_query(m_Mysql,sql))//失败返回非零值 
    {
        cerr<<"查询出错："<<mysql_error(m_Mysql)<<endl;
        return false;
    }else{
        cout<<sql<<"查询结果如下："<<endl;
    }
    m_SelectResult = mysql_store_result(m_Mysql);
    return true;
} 
//从数据库加载好友信息到通讯录容器
map<string,Friend> MysqlManager::loadContact(){
    const char* sql = "SELECT * FROM friend";
    selectData(sql);
    map<string,Friend> contact{};
    Friend fri{};
    MYSQL_ROW row;//结果集返回数据库中的一行记录 - row为NULL时，表示已无更多行
    while(row = mysql_fetch_row(m_SelectResult)){
        strcpy(fri.name,row[1]);//数据库内设置的行row[1]为name
        fri.id = atoi(row[0]);//row[0]为id - 数组,atoi()函数将数组转换为int
        strcpy(fri.phone,row[2]);//row[2]为phone
        strcpy(fri.address,row[3]);//row[3]为address
        contact[fri.phone] = fri;//放入map中
    }
    return contact;
}
//保存好友信息到数据库
void MysqlManager::saveContact(const map<string,Friend>& contact){
    //先清空friend表
    const char* clearFriendTable = "DELETE FROM friend";
    if(!runSql(clearFriendTable))//返回0表示失败
    {
        cout<<"清空好友表失败!"<<endl;
        return;
    }else{
        cout<<"好友表已被清空！"<<endl;
    }

    //再保存最新的好友信息到数据库friend表中
    const char* insertFriendSql = "INSERT INTO friend(name,phone,address) values(?,?,?)";
    MYSQL_STMT* stmt = mysql_stmt_init(m_Mysql);//初始化预处理语句
    if(!stmt){
        cout<<"初始化预处理语句失败!"<<endl;
        return;
    }
    if(mysql_stmt_prepare(stmt,insertFriendSql,strlen(insertFriendSql)))//返回1表示失败
    {
        cout<<"预处理语句准备失败:"<<mysql_stmt_error(stmt)<<endl;
        mysql_stmt_close(stmt);//关闭预处理语句
        return;
    }

    MYSQL_BIND bind[3];
    memset(bind,0,sizeof(bind));//清空内存
    char name[20];//替换?占位符
    char phone[20];
    char address[100];
    unsigned long name_len,phone_len,address_len;//实际长度
    bind[0].buffer_type = MYSQL_TYPE_STRING;//缓冲类型
    bind[0].buffer = name;//缓冲名字
    bind[0].buffer_length = sizeof(name);//缓冲长度
    bind[0].length = &name_len;//数据实际长度
    bind[1].buffer_type = MYSQL_TYPE_STRING;
    bind[1].buffer = phone;
    bind[1].buffer_length = sizeof(phone);
    bind[1].length = &phone_len;
    bind[2].buffer_type = MYSQL_TYPE_STRING;
    bind[2].buffer = address;
    bind[2].buffer_length = sizeof(address);
    bind[2].length = &address_len;

    if(mysql_stmt_bind_param(stmt,bind))//将参数绑定到预处理语句，1表示失败
    {
        cout<<"绑定参数失败!"<<mysql_stmt_error(stmt)<<endl;
        mysql_stmt_close(stmt);
        return;
    }

    for(const auto& pair:contact)//保存好友信息
    {
        const Friend& fri = pair.second;
        strncpy(name,fri.name,sizeof(name));//复制到缓冲中
        name_len = strlen(name);
        strncpy(phone,fri.phone,sizeof(phone));
        phone_len = strlen(phone);
        strncpy(address,fri.address,sizeof(address));
        address_len = strlen(address);
        if(mysql_stmt_execute(stmt)){
            cout<<"预处理语句执行失败："<<mysql_stmt_error(stmt)<<endl;
        }else{
            cout<<"成功插入了好友:"<<name<<endl;
        }
    }
    mysql_stmt_close(stmt);
}



void ContactUtil::saveContact() const {
    ofstream file("friend.dat", ios::binary);
    if (!file) {
        cout << "文件打开失败" << endl;
        return;
    }
    for (auto pair : ContactUtil::contacts) {
        file.write((char *)&pair.second, sizeof(Friend));
    }
    file.close();
    dirtyFlag = false;
}

void ContactUtil::loadContacts()
{
    ifstream file("friend.dat", ios::binary);
    if (!file)
    {
        cout << "文件打开失败" << endl;
        return;
    }
    Friend temp;
    while (file.read((char *)&temp, sizeof(temp)))
    {
        contacts[temp.phone] = temp;
    }
    file.close();
    dirtyFlag = false;
}

void ContactUtil::addContact()
{
    Friend fri;
    cout << "请输入好友姓名：" << endl;
    cin >> fri.name;
    cout << "请输入好友电话：" << endl;
    cin >> fri.phone;
    for (auto i : contacts)
    {
        if (strcmp(i.second.phone, fri.phone) == 0)
        {
            cout << "该电话号码已存在" << endl;
            return;
        }
    }
    cout << "请输入好友地址：" << endl;
    cin >> fri.address;
    contacts[fri.phone] = fri; //
    dirtyFlag = true;
}

void ContactUtil::deleteContact()
{
    char *phone = new char[16];
    cout << "请输入要删除的联系人电话：" << endl;
    cin >> phone;
    auto it = contacts.find(phone);
    if (it == contacts.end())
    {
        cout << "该联系人不存在" << endl;
        return;
    }
    cout<<"是否确定删除该联系人(y/n):"<<endl;
    char ch;
    cin>>ch;
    if(ch=='y' || ch=='Y')
    {
        contacts.erase(it);
        cout<<"联系人删除成功!"<<endl;
    }   
    delete[] phone;
    dirtyFlag = true;
}

//搜索联系人
void ContactUtil::searchContact() const {
    char *phone = new char[16];
    cout << "请输入要查找的联系人电话：" << endl;
    cin >> phone;
    auto it = contacts.find(phone);
    if (it == contacts.end()) {
        cout << "该联系人不存在" << endl;
        delete[] phone; // 释放动态分配的内存
        return;
    } else {
        cout << "联系人姓名:" << it->second.name << endl;
        cout << "联系人地址:" << it->second.address << endl;
        delete[] phone; // 释放动态分配的内存
        }
        
}

//更新联系人
void ContactUtil::updateContact(map<string, Friend>& contact) {
    char *phone = new char[16];
    char ch = 'n';
    cout << "是否要修改联系人手机号码(Y/N):" << endl;
    cin >> ch;
    if (ch == 'Y' || ch == 'y') {
        cout << "请输入要修改的联系人电话:" << endl;
        cin >> phone;
        auto it = contact.find(phone);
        if (it == contact.end()) {
            cout << "该联系人不存在" << endl;
            delete[] phone; // 释放动态分配的内存
            return;
        } else {
            char *newPhone = new char[16];
            cout << "请输入新的电话号码:" << endl;
            cin >> newPhone;
            // 检查新旧号码是否相同
            if (strcmp(phone, newPhone) == 0) {
                cout << "新的手机号与原手机号相同，无需更新。" << endl;
                delete[] phone; // 释放动态分配的内存
                delete[] newPhone; // 释放动态分配的内存
                return;
            }
            cout << "正在查看新的手机号是否已存在:" << endl;
            auto newIt = contact.find(newPhone);
            if (newIt != contact.end()) {
                cout << "新的手机号已经存在，无法更新。" << endl;
                delete[] phone; // 释放动态分配的内存
                delete[] newPhone; // 释放动态分配的内存
                return;
            } else {
                // 更新联系人的手机号码
                contact[newPhone] = it->second; // 添加新号码对应的联系人
                contact.erase(it); // 删除旧号码对应的联系人信息
                dirtyFlag = true;

                cout << "手机号更新成功。" << endl;
            }
            delete[] phone; // 释放动态分配的内存
            delete[] newPhone; // 释放动态分配的内存
        }
    } else {
        cout << "是否修改联系人信息(Y/N):" << endl;
        cin >> ch;
        if (ch == 'Y' || ch == 'y') {
           cout<<"请输入要修改的联系人电话:"<<endl;
             cin>>phone;
              auto it = contact.find(phone);
                if (it == contact.end()) {
                    cout<<"该联系人不存在"<<endl;
                    delete[] phone; // 释放动态分配的内存

               }  else {
                    cout<<"请输入要修改的联系人姓名:"<<endl;
                    cin>>it->second.name;
                    cout<<"请输入要修改的联系人地址:"<<endl;
                    cin>>it->second.address;
                }     
    }
     delete[] phone; // 释放动态分配的内存
    }
}

void printMenu()
{
    cout<<"┍------欢迎使用管理系统-----┑"<<endl;
    cout<<"|          1.登录           |"<<endl;
    cout<<"|          2.注册           |"<<endl;
    cout<<"|          0.退出系统       |"<<endl;
    cout<<"┕---------------------------┙"<<endl;
}

void Register() {
    string username, password;
    cout << "请输入用户名: ";
    cin >> username;

    if (!IsUsernameAvailable(username)) {
        cout << "用户名已存在，请选择其他用户名。" << endl;
        return;
    }

    cout << "请输入密码: ";
    cin >> password;

    ofstream file("users.txt", ios::app); // 以追加模式打开文件
    if (file.is_open()) {
        file << username << " " << password << endl;
        file.close();
        cout << "注册成功！" << endl;
    } else {
        cout << "无法打开文件进行写入。" << endl;
    }
}

bool IsUsernameAvailable(const string& username) {
    ifstream file("users.txt");
    string storedUsername, storedPassword;
    while (file >> storedUsername >> storedPassword) {
        if (storedUsername == username) {
            return false; // 用户名已存在
        }
    }
    return true; // 用户名可用
}

void Login() {
    string username, password;
    int attemptCount = 0;
    const int maxAttempts = 3;
    int count_a=3;
    while (attemptCount < maxAttempts) {
        cout << "请输入用户名: ";
        cin >> username;
        cout << "请输入密码: ";
        cin >> password;

        ifstream file("users.txt");
        string storedUsername, storedPassword;
        bool loginSuccess = false;
        while (file >> storedUsername >> storedPassword) {
            if (storedUsername == username && storedPassword == password) {
                loginSuccess = true;
                break;
            }
        }
        file.close();

        if (loginSuccess) {
            cout << "登录成功！" << endl;
            return;
        } else {
            attemptCount++;
            count_a--;
            cout << "用户名或密码错误，请重新输入，你还有"<<count_a<<"次机会" << endl;
        }
    }
    if(count_a==0)
   { 
    cout << "用户名或密码错误次数过多，已退出程序。" << endl;
    exit(0); // 退出程序
    }
}


void menu_m(MysqlManager &mysql,ContactUtil &util)
{
    util.contacts = mysql.loadContact();
    cout << "好友通信名单" <<  endl;
    for (auto pair : util.contacts) {
         cout << "手机号:" << pair.first << "---姓名:" << pair.second.name << ",地址:" << pair.second.address <<  endl;
    }
    int choice;
    do {
         cout << "1.添加好友信息" << endl;
         cout << "2.删除好友信息" << endl;
         cout << "3.修改好友信息" << endl;
         cout << "4.查找好友信息" << endl;
         cout << "5.退出" <<  endl;
         cout << "6.保存" <<  endl;
         cout << "7.从本地加载" <<  endl;
         cout << "8.从Mysql加载" <<  endl;
         cin >> choice;
        switch (choice) {
            case 1:
                util.addContact();
                break;
            case 2:
                util.deleteContact();
                break;
            case 3:
                util.updateContact(util.contacts);
                break;
            case 4:
                util.searchContact();
                break;
            case 5:
             if (dirtyFlag)
            {
                cout << "是否保存修改？Y/N" << endl;
                char c;
                cin >> c;
                if (c == 'y' || c == 'Y')
                {
                    util.saveContact();
                    mysql.saveContact(util.contacts);
                }
                else
                {
                    cout << "已放弃保存" << endl;
                }
                }             
                break;
            case 6:
                util.saveContact();
                mysql.saveContact(util.contacts);
            break;
        case 7:
            util.loadContacts();
            for(const auto& pair:util.contacts){
                 cout<<"好友信息："<<pair.second.name<<","<<pair.second.phone<<","<<pair.second.address<<endl;
                 }
            break;
        case 8:
            util.contacts = mysql.loadContact();
             for(const auto& pair:util.contacts){
                 cout<<"好友信息："<<pair.second.name<<","<<pair.second.phone<<","<<pair.second.address<<endl;
                 }
            break;
        default:
            cout << "输入错误" << endl;
            break;
        }
    } while (choice != 5);   
}


int main(){
   ContactUtil util ;

    MysqlManager mysql;
    mysql.m_host = "127.0.0.1";//localhost
    mysql.m_username = "root";
    mysql.m_password = "xx14151611";
    mysql.m_database = "contact";  
    mysql.m_port = 3306;
    mysql.m_unix_socket = NULL;
    mysql.m_clientflag = 0;
    if(!mysql.init())//连接数据库失败返回false
    {
        return -1;
    }  

    while (1)
    {      
        int select = 0;
        printMenu();
        // 读取用户输入
        cin >> select;
        switch (select)
        {
            case 0: // 退出
                cout << "退出成功！" << endl;
                exit(0); // 退出程序
                break;
            case 1: // 登录
                cout << "登录" << endl;  
                Login();
                menu_m(mysql,util);
                break;
            case 2: // 注册
                cout << "注册" << endl;
                Register();
                break;
            default:
                cout << "无效选择，请重新输入。" << endl;
                break;
        }
    }

    mysql.freeConnect();
    return 0;
}



    