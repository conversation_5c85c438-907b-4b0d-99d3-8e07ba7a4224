using System;
using System.Text;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using Script.Methods;
/************************************
Shell Module default code: using .NET Framwwork 4.6.1
钩扁特征值计算和日志记录脚本
根据角度dangle和钩外径dis计算特征值并存储到每日日志文件
存储位置：D:\HODD\
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
	//执行次数计数
    int processCount;

    // 输入变量
    public string dangle { get; set; }    // 角度字符串
    public string dis { get; set; }       // 钩外径字符串
    public object pic { get; set; }       // 图像对象
    public string picNam { get; set; }    // 照片名称

    // 特征向量存储
    public double[] FeatureVector { get; private set; }  // 综合特征向量

    // 配置参数
    private int vectorDimension = 12;      // 特征向量维度
    private double angleWeight = 0.5;      // 角度特征权重
    private double distanceWeight = 0.5;   // 距离特征权重

    // 日志存储路径
    private string logRootPath = @"D:\HODD";

    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;

        // 确保日志目录存在
        EnsureLogDirectoryExists();
    }

    /// <summary>
    /// 确保日志目录存在
    /// </summary>
    private void EnsureLogDirectoryExists()
    {
        try
        {
            if (!Directory.Exists(logRootPath))
            {
                Directory.CreateDirectory(logRootPath);
            }
        }
        catch (Exception ex)
        {
            // 如果创建目录失败，记录到系统日志或输出
            Console.WriteLine("创建日志目录失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 解析特征值字符串为数值列表
    /// </summary>
    /// <param name="featureStr">特征值字符串（分号分隔）</param>
    /// <returns>数值列表</returns>
    private List<double> ParseFeatureString(string featureStr)
    {
        List<double> result = new List<double>();
        if (string.IsNullOrEmpty(featureStr)) return result;

        string[] parts = featureStr.Split(';');
        foreach (string part in parts)
        {
            double value;
            if (double.TryParse(part.Trim(), out value))
            {
                result.Add(value);
            }
        }
        return result;
    }

    /// <summary>
    /// 提取图像基本特征
    /// </summary>
    /// <param name="image">图像对象</param>
    /// <returns>图像特征数组</returns>
    private double[] ExtractImageFeatures(object image)
    {
        List<double> imageFeatures = new List<double>();
        try
        {
            if (image != null)
            {
                // 使用反射获取VisionMaster图像属性
                var imageType = image.GetType();
                var widthProp = imageType.GetProperty("Width");
                var heightProp = imageType.GetProperty("Height");
                var meanProp = imageType.GetProperty("Mean");
                var stdDevProp = imageType.GetProperty("StdDev");

                // 提取基本统计特征
                if (widthProp != null && heightProp != null)
                {
                    imageFeatures.Add(Convert.ToDouble(widthProp.GetValue(image)));
                    imageFeatures.Add(Convert.ToDouble(heightProp.GetValue(image)));
                }
                if (meanProp != null)
                    imageFeatures.Add(Convert.ToDouble(meanProp.GetValue(image)));
                if (stdDevProp != null)
                    imageFeatures.Add(Convert.ToDouble(stdDevProp.GetValue(image)));
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("图像特征提取失败: " + ex.Message);
        }
        return imageFeatures.ToArray();
    }

    /// <summary>
    /// 计算特征值统计特征
    /// </summary>
    /// <param name="values">数值列表</param>
    /// <returns>统计特征数组</returns>
    private double[] CalculateStatisticalFeatures(List<double> values)
    {
        if (values == null || values.Count == 0)
            return new double[6];

        return new double[]
        {
            values.Min(),          // 最小值
            values.Max(),          // 最大值
            values.Average(),      // 平均值
            values.StdDev(),       // 标准差
            values.Median(),       // 中位数
            values.Count           // 数量
        };
    }

    /// <summary>
    /// 提取综合特征向量
    /// </summary>
    /// <param name="angles">角度列表</param>
    /// <param name="distances">距离列表</param>
    /// <param name="image">图像对象</param>
    private void ExtractFeatureVector(List<double> angles, List<double> distances, object image)
    {
        List<double> features = new List<double>();

        // 提取角度统计特征并加权
        double[] angleStats = CalculateStatisticalFeatures(angles);
        foreach (var stat in angleStats)
            features.Add(stat * angleWeight);

        // 提取距离统计特征并加权
        double[] distanceStats = CalculateStatisticalFeatures(distances);
        foreach (var stat in distanceStats)
            features.Add(stat * distanceWeight);

        // 提取图像特征
        double[] imageFeatures = ExtractImageFeatures(image);
        features.AddRange(imageFeatures);

        // 调整特征向量维度
        FeatureVector = AdjustVectorDimension(features.ToArray());
    }

    /// <summary>
    /// 调整特征向量维度至指定长度
    /// </summary>
    private double[] AdjustVectorDimension(double[] vector)
    {
        if (vector.Length == vectorDimension)
            return vector;

        // 维度不足则补零，过长则截断
        double[] adjusted = new double[vectorDimension];
        int copyLength = Math.Min(vector.Length, vectorDimension);
        Array.Copy(vector, adjusted, copyLength);
        return adjusted;
    }

    /// <summary>
    /// 计算特征值统计信息
    /// </summary>
    /// <param name="angles">角度列表</param>
    /// <param name="distances">距离列表</param>
    /// <returns>特征值描述字符串</returns>
    private string CalculateFeatureStatistics(List<double> angles, List<double> distances)
    {
        StringBuilder result = new StringBuilder();

        if (angles.Count > 0)
        {
            double[] stats = CalculateStatisticalFeatures(angles);
            result.AppendFormat("角度统计[数量:{5}, 最小:{0:F3}, 最大:{1:F3}, 平均:{2:F3}, 标准差:{3:F3}, 中位数:{4:F3}]",
                stats[0], stats[1], stats[2], stats[3], stats[4], stats[5]);
        }

        if (distances.Count > 0)
        {
            double[] stats = CalculateStatisticalFeatures(distances);
            result.AppendFormat(" 距离统计[数量:{5}, 最小:{0:F3}, 最大:{1:F3}, 平均:{2:F3}, 标准差:{3:F3}, 中位数:{4:F3}]",
                stats[0], stats[1], stats[2], stats[3], stats[4], stats[5]);
        }

        if (FeatureVector != null && FeatureVector.Length > 0)
        {
            result.Append(" 特征向量维度:" + FeatureVector.Length);
        }

        return result.ToString();
    }

    /// <summary>
    /// 写入每日日志文件
    /// </summary>
    /// <param name="featureInfo">特征值信息</param>
    /// <param name="pictureName">照片名称</param>
    private void WriteToDailyLog(string featureInfo, string pictureName)
    {
        try
        {
            DateTime now = DateTime.Now;
            string dateStr = now.ToString("yyyy-MM-dd");
            string logFileName = "HookFeature_" + dateStr + ".log";
            string logFilePath = Path.Combine(logRootPath, logFileName);

            string timeStr = now.ToString("HH:mm:ss.fff");
            string logEntry = string.Format("[{0}] 照片:{1} | 特征值:{2}",
                timeStr, pictureName, featureInfo);

            // 追加写入日志文件
            File.AppendAllText(logFilePath, logEntry + Environment.NewLine, Encoding.UTF8);
        }
        catch (Exception ex)
        {
            Console.WriteLine("写入日志失败: " + ex.Message);
        }
    }

    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>
    public bool Process()
    {
        try
        {
            processCount++;

            // 检查输入参数
            if (string.IsNullOrEmpty(dangle) || string.IsNullOrEmpty(dis) || string.IsNullOrEmpty(picNam))
            {
                Console.WriteLine("输入参数不完整: dangle=" + (dangle ?? "null") +
                    ", dis=" + (dis ?? "null") + ", picNam=" + (picNam ?? "null"));
                return false;
            }

            // 解析角度和距离特征值
            List<double> angles = ParseFeatureString(dangle);
            List<double> distances = ParseFeatureString(dis);

            if (angles.Count == 0 && distances.Count == 0)
            {
                Console.WriteLine("特征值解析失败，无有效数据");
                return false;
            }

            // 提取综合特征向量
        ExtractFeatureVector(angles, distances, pic);

        // 计算特征值统计信息
        string featureStatistics = CalculateFeatureStatistics(angles, distances);

        // 写入每日日志
        WriteToDailyLog(featureStatistics, picNam);

        // 调试输出特征向量
        if (FeatureVector != null)
        {
            Console.WriteLine("特征向量提取完成，维度: {0}", FeatureVector.Length);
        }

            Console.WriteLine("处理完成 - 照片: " + picNam + ", 特征值已记录");

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine("处理过程中发生错误: " + ex.Message);
            return false;
        }
    }
}
                            