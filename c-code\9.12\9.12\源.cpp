#include <iostream>
using namespace std;

//饿汉式单例设计模式
class Singleton
{
private:
    static Singleton* instance; // 静态成员变量保存唯一实例，存储在静态存储区中
    Singleton() {}              // 私有构造函数，防止外部调用
public:
    static Singleton* getInstance()
    {
        return instance;
    }
};
Singleton* Singleton::instance = new Singleton(); // 静态成员的初始化，不受私有访问限制，返回的是指针，返回一个指向在堆内存分配的空间地址，所以用*(new)

int main()
{
    Singleton* sin = Singleton::getInstance();
    Singleton* sin1 = Singleton::getInstance();
    if (sin == sin1)
    {
        cout << "多次访问getInstance函数，得到的是同一个实例" << endl;
    }
}