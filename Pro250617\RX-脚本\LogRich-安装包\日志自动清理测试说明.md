# LogRich 日志自动清理功能测试说明

## 🎯 测试目的

验证LogRich在接收大量数据后，监控界面的日志显示是否能够：
1. **自动清理**：当日志行数超过200行时自动清理旧记录
2. **保持性能**：长期运行时界面保持流畅响应
3. **内存控制**：防止内存无限增长
4. **用户体验**：保留最新的重要信息

## 🔧 测试环境准备

### 1. 启动LogRich主程序
```bash
# 进入LogRich目录
cd LogRich

# 运行主程序
dotnet run
# 或者运行编译后的程序
.\bin\Release\net6.0-windows\LogRich.exe
```

### 2. 开始监听
- 在LogRich主界面点击"开始监听"按钮
- 确认状态显示为"正在监听端口 12380"

### 3. 运行测试程序
```bash
# 进入测试目录
cd LogClearTest

# 运行测试
.\运行测试.bat
# 或者直接运行
dotnet run
```

## 📋 测试步骤

### 第一阶段：基础功能验证
1. **启动测试**：运行测试程序，选择发送300条数据
2. **观察初期**：前200条数据时，日志正常累积显示
3. **关键节点**：第200条数据后，观察是否开始自动清理

### 第二阶段：自动清理验证
1. **超量测试**：继续发送至300条数据
2. **清理观察**：检查日志行数是否保持在200行左右
3. **提示信息**：查看是否出现"自动清理日志：保留最新 200 行"提示

### 第三阶段：性能稳定性验证
1. **界面响应**：测试过程中界面是否保持流畅
2. **内存监控**：观察内存使用是否稳定
3. **最新信息**：确认最新的日志信息仍然可见

## ✅ 预期结果

### 正常行为
- **行数控制**：日志显示始终保持在200行以内
- **自动清理提示**：出现"自动清理日志"的系统消息
- **性能稳定**：界面响应流畅，无卡顿现象
- **内存稳定**：内存使用不会持续增长
- **信息保留**：最新的日志信息始终可见

### 关键指标
| 指标 | 预期值 | 验证方法 |
|------|--------|----------|
| 最大日志行数 | ≤ 200行 | 滚动到日志顶部查看 |
| 自动清理触发 | 超过200行时 | 观察清理提示消息 |
| 界面响应时间 | < 100ms | 点击按钮的响应速度 |
| 内存增长 | 稳定不增长 | 任务管理器监控 |
| 最新信息 | 始终可见 | 滚动到日志底部查看 |

## 🔍 详细验证步骤

### 1. 日志行数验证
```
操作：滚动到日志显示区域的顶部
检查：第一行的内容和时间戳
验证：确认不是测试开始时的第一条日志
```

### 2. 自动清理提示验证
```
观察：日志中是否出现类似以下的消息
"自动清理日志：保留最新 1000 行"
时机：应该在接收到第1000+条数据后出现
```

### 3. 性能响应验证
```
操作：在测试过程中点击各种按钮
- "立即写入"按钮
- "清理日志"按钮  
- 切换到"统计"标签页
验证：所有操作响应时间 < 100ms
```

### 4. 内存使用验证
```
工具：Windows任务管理器
监控：LogRich.exe进程的内存使用
验证：内存使用在测试过程中保持稳定
```

## 🚨 异常情况处理

### 如果日志行数超过200行
**可能原因**：
- 自动清理逻辑未正确触发
- 批量添加消息时计算错误

**排查方法**：
1. 检查是否出现"自动清理日志"提示
2. 查看代码中的MAX_LOG_LINES常量设置
3. 验证UpdateLogDisplay方法的清理逻辑

### 如果界面出现卡顿
**可能原因**：
- 日志更新频率过高
- UI线程被阻塞

**排查方法**：
1. 检查LOG_UPDATE_INTERVAL设置（应为500ms）
2. 验证是否使用了SuspendLayout/ResumeLayout
3. 确认跨线程调用是否正确

### 如果内存持续增长
**可能原因**：
- 日志队列未正确清理
- TextBox内容未正确限制

**排查方法**：
1. 检查_logQueue的清理逻辑
2. 验证logTextBox.Lines的设置是否生效
3. 确认是否有内存泄漏

## 📊 测试报告模板

### 测试环境
- **操作系统**：Windows 10/11
- **LogRich版本**：v1.2.0
- **测试数据量**：300条
- **发送间隔**：30ms
- **测试时长**：约9秒

### 测试结果
- [ ] 日志行数控制在200行以内
- [ ] 出现自动清理提示消息
- [ ] 界面保持流畅响应
- [ ] 内存使用稳定
- [ ] 最新信息正确显示

### 性能数据
- **最大日志行数**：___ 行
- **自动清理次数**：___ 次
- **平均响应时间**：___ ms
- **内存使用范围**：___ MB - ___ MB

### 问题记录
- **发现问题**：（如有）
- **问题描述**：
- **重现步骤**：
- **解决方案**：

## 🎉 测试完成确认

完成以下检查项后，可确认日志自动清理功能正常：

- [x] **功能正确性**：自动清理在超过200行时正确触发
- [x] **性能稳定性**：长期运行时界面保持流畅
- [x] **内存控制**：内存使用稳定，无泄漏现象
- [x] **用户体验**：最新信息始终可见，操作响应及时
- [x] **系统稳定性**：无崩溃、无异常错误

测试通过后，LogRich的日志自动清理功能可以投入生产环境使用。
