{
	"version": "2.0.0",
	"tasks": [
		{
			"type": "shell",
			"label": "opencv4.5.5",
			"command": "F:/soft/opencv/mingw64/bin/g++.exe",
			"args": [
				/*
				"-fdiagnostics-color=always",
				*/
				"-g",
				"${file}",
				"-o",
				"${fileDirname}\\${fileBasenameNoExtension}.exe",

				"F:/soft/opencv/opencv/build/x64/MinGW/bin/libopencv_world455.dll",
				"-I",
				"F:/soft/opencv/opencv/build/x64/MinGW/install/include",
				"-I",
				"F:/soft/opencv/opencv/build/x64/MinGW/install/include/opencv2",
				/*
				"-L",
				"F:/soft/opencv/opencv/build/x64/MinGW/bin",
				"-l",
				"libopencv_world455",
				"-l",
				"opencv_videoio_ffmpeg455_64"
				*/
			],
			"options": {
				"cwd": "F:/soft/opencv/mingw64/bin"
			},
			"problemMatcher": [
				"$gcc"
			],
			"group": "build",
			//"isDefault": true
			
			"detail": "编译器: F:\\soft\\opencv\\mingw64\\bin\\g++.exe"
			
		}
	]
}