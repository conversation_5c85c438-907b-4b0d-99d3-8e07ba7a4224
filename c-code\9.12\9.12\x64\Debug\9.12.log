﻿  test.c
F:\c-code\9.12\9.12\test.c(15,9): warning C4477: “printf”: 格式字符串“%d”需要类型“int”的参数，但可变参数 1 拥有了类型“size_t”
F:\c-code\9.12\9.12\test.c(15,9): message : 请考虑在格式字符串中使用“%zd”
F:\c-code\9.12\9.12\test.c(12,2): error C4996: 'strcpy': This function or variable may be unsafe. Consider using strcpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
F:\c-code\9.12\9.12\test.c(11,1): warning C4047: “初始化”:“char”与“char *”的间接级别不同
