# LogRich v1.2.1 - 工业检测数据管理系统

## 📋 系统概述

LogRich是一个专为工业生产环境设计的检测数据管理系统，通过TCP Socket接收检测设备数据，进行实时处理、存储和统计分析。

### 🎯 v1.2.1 新特性
- **200行日志限制**：严格控制内存使用，适合资源受限环境
- **自动清理功能**：超过200行时自动清理旧记录
- **性能优化**：提升界面响应速度和长期运行稳定性

## 主要功能

1. **Socket监听服务** - 在端口12380监听外部程序连接
2. **数据接收和解析** - 解析检测信息并验证格式
3. **合规性检查** - 验证必需字段(PM、SN、TS、C1)是否存在
4. **CSV文件存储** - 按产品型号和日期组织存储检测数据
5. **统计分析** - 计算良率、产量和分类统计
6. **用户界面** - 提供监控和统计功能的图形界面

## 合规性检查

系统对接收到的检测信息执行严格的合规性检查：

### 必需字段
- **PM** (产品型号) - 不能为空
- **SN** (流水号) - 不能为空  
- **TS** (检测时间) - 必须符合格式 `yyyyMMdd-HHmmss-fff`
- **C1** (相机1结果) - 必须存在且为有效数字

### 处理规则
- 缺少任何必需字段的数据将被忽略
- 系统会记录详细的日志信息说明拒绝原因
- 只有通过合规性检查的数据才会被保存到CSV文件
- 不符合要求的数据不会影响统计计算

## 数据格式

### 输入数据格式
```
PM:产品型号;SN:流水号;TS:检测时间;相机判定分类...
```

**示例：**
```
PM:0523A;SN:1005;TS:20250616-093021-233;C1:1;C2:1;C2A:1;C3:3;C4:1;C5:1;C5A:1;
```

### CSV存储格式
- 文件名：`PRec_{产品型号}_{日期}.csv`
- 字段：流水号,检测时间,C1,C2,C2A,C3,C4,C5,C5A...

### 判定分类说明
- **0**: 异常
- **1**: 良品
- **2-9**: NG类别

## 系统要求

- .NET 6.0 或更高版本
- Windows操作系统
- 端口12380可用

## 编译和运行

### 编译主程序
```bash
dotnet build LogRich.csproj
dotnet run
```

### 编译测试客户端
```bash
cd TestClient
dotnet build TestClient.csproj
dotnet run
```

## 使用说明

### 1. 启动应用程序
- 运行LogRich.exe
- 应用程序将显示主界面，包含"监控"和"统计"两个标签页

### 2. 开始监听
- 在"监控"标签页中点击"开始监听"按钮
- 系统将在端口12380开始监听外部连接
- 状态显示为"正在监听端口 12380"

### 3. 发送检测数据
- 外部程序通过Socket连接到端口12380
- 发送符合格式的检测信息
- 系统自动解析并保存到对应的CSV文件

### 4. 查看统计
- 切换到"统计"标签页
- 选择产品型号和日期范围
- 点击"计算统计"查看结果：
  - 总产量和总体良率
  - 各相机良率表格
  - 各判定分类数量统计

## 测试

使用提供的测试客户端验证功能：

1. 先启动LogRich主程序并开始监听
2. 运行测试客户端：
   ```bash
   cd TestClient
   dotnet run
   ```
3. 测试客户端将发送示例数据到主程序
4. 在主程序中观察接收到的数据和生成的CSV文件

### 合规性检查测试

测试客户端包含10个测试用例来验证合规性检查功能：
- 3个有效数据案例（应被接受）
- 7个无效数据案例（应被拒绝）

预期结果：
- 有效数据会被保存到CSV文件
- 无效数据会被忽略并记录相应的日志信息

## 数据存储

- CSV文件存储在`Data`目录下
- 按产品型号和日期自动分类
- 文件格式便于Excel等工具打开和分析

## 故障排除

1. **端口被占用**：确保端口12380未被其他程序使用
2. **连接失败**：检查防火墙设置
3. **数据解析失败**：确认输入数据格式正确
4. **合规性检查失败**：确保包含所有必需字段(PM、SN、TS、C1)
5. **文件访问错误**：确保有足够的磁盘空间和写入权限

## 技术架构

- **前端**：Windows Forms
- **网络**：TCP Socket
- **数据存储**：CSV文件
- **框架**：.NET 6.0

## 文件结构

```
LogRich/
├── Models/
│   └── DetectionInfo.cs      # 数据模型
├── Services/
│   ├── SocketListener.cs     # Socket监听服务
│   ├── DataManager.cs        # 数据管理
│   └── StatisticsCalculator.cs # 统计计算
├── TestClient/               # 测试客户端
├── MainForm.cs              # 主窗体
├── Program.cs               # 程序入口
└── README.md               # 说明文档
```