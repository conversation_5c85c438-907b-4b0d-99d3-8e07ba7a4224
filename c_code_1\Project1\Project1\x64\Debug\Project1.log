﻿  map.cpp
  MysqlManager.cpp
  正在生成代码...
map.obj : error LNK2001: 无法解析的外部符号 "public: static class std::map<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,struct Friend,struct std::less<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > >,class std::allocator<struct std::pair<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > const ,struct Friend> > > ContactUtil::contacts" (?contacts@ContactUtil@@2V?$map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UFriend@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UFriend@@@std@@@2@@std@@A)
F:\CDoc\c_code_1\Project1\x64\Debug\Project1.exe : fatal error LNK1120: 1 个无法解析的外部命令
