#define _CRT_SECURE_NO_WARININGS
#include<iostream>
#include<string>
using namespace std;

////代理模式
//class Subject {
//public:
//	virtual void exam() = 0;
//	virtual ~Subject() {}
//};
//class RealSubject :public Subject {
//public:
//	void exam() override{
//		cout << "考试请求通过" << endl;
//	}
//};
//class Proxy :public Subject {
//private:
//	RealSubject* real;
//	string select;
//public:
//	Proxy() {
//		real = new RealSubject();
//	}
//	~Proxy() {
//		delete real;
//	}
//	//void Select(string s2)
//	//{
//	//	select = s2;
//	//}
//	///*Proxy& operator>>(istream input) {
//	//	input >> select;
//	//	return *this;
//	//}*/
//	void exam()override {
//		string test;
//		cout << "代理考试申请中..." << endl;
//		cout <<	"是否通过申请？（y/n）" << endl;
//		cin >> select;
//		if (select == "y" || select == "Y")
//		{
//			real->exam();
//			cout << "处理中..." << endl;
//		}
//		else
//			cout << "申请失败" << endl;
//	}
//};
//void clientRequest(Subject &s1)
//{
//	s1.exam();
//}
//int main()
//{
//	Proxy p1;
//	clientRequest(p1);
//}


////策略模式
//class Strategy {
//public:
//	virtual void execute() = 0;
//	virtual ~Strategy() = 0;
//};
//Strategy::~Strategy() {}
//class StrategyA:public Strategy {
//public:
//	void execute()override {
//		cout << "模式A运行..." << endl;
//	}
//};
//class StrategyB :public Strategy {
//public:
//	void execute()override {
//		cout << "模式B运行" << endl;
//	}
//};
//class Context {
//private:
//	Strategy* strategy;
//public:
//	Context() :strategy{} {};
//	Context(Strategy* s) :strategy{ s } {};
//	Strategy* getStrategy()const {
//		return strategy;
//	}
//	void setStrategy(Strategy* str) {
//		strategy = str;
//	}
//	void process() {
//		cout << "执行中前..." << endl;
//		strategy->execute();
//		cout << "执行后..." << endl;
//	}
//};
//int main()
//{
//	StrategyA strA{};
//	
//	Context con{ };
//	StrategyB strB{};
//}

//template<typename T>
//T ass(T a1, T a2)
//{
//	return a1 + a2;
//}
//int main()
//{
//	cout << ass(1.1, 1.1) << endl;
//	cout <<  ass(1, 1) << endl;
//}

//template<typename T>
//void bubbleSort(T* A, int n)
//{
//	for (int i = 0; i < n - 1; i++)
//	{
//		for (int j = 0; j < n - 1 - i;j++)
//		{
//			if (A[j] > A[j + 1])
//			{
//				int temp = A[j];
//				A[j] = A[j + 1];
//				A[j + 1] = temp;
//			}
//		}
//	}
//}
//int main()
//{
//	int A[5] = { 1,9,6,5,7 };
//	int n = sizeof(A) / sizeof(int);
//	bubbleSort(A, n);
//	for (int i = 0; i < n; i++)
//	{
//		cout << A[i] << "";
//	}
//	cout << endl;
//	return 0;
//}

template<class T>
class DynamicArray {
private:
	T* data;
	int size;
	int capacity;
	void resize(int new_capacity) {
		T* new_data = new T[new_capacity];
		for (int i = 0; i < size; i++)
		{
			new_data[i] = data[i];
		}
		delete[] data;
		data = new_data;
		capacity = new_capacity;
	}
public:
	DynamicArray() :size(0), capacity(1){
		data = new T[capacity];
	}
	~DynamicArray() {
		delete[] data;
	}
	void add(const T& value) {
		if (size == capacity)
			resize(2 * capacity);
		data[size++] = value;
	}

};
int main()
{
	;
}