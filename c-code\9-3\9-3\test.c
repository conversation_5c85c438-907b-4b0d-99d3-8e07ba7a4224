#include<stdio.h>
#include<stdlib.h>

//int main()
//{
//    int* ptr = NULL;
//    ptr = (int*)malloc(sizeof(int));
//    if (NULL == ptr)
//    {
//        perror("error");
//        return 1;
//    }
//    //给动态内存上配的变量初始化
//    *ptr = 666;
//    printf("%d\n", *ptr);
//    free(ptr);
//    ptr = NULL;
//    return 0;
//}

//Node
//#include<stdlib.h>
//
//typedef struct st_node {
//	int date;
//	struct st_node * next;
//}Node,*Linklist;
//
//Linklist creat_linklist()
//{
//	Linklist header=(Linklist)calloc(8,sizeof(Node));
//	if (NULL == header)
//	{
//		perror("error creat header");
//		return NULL;
//	}
//	header->next = NULL;
//	printf("%d\n", header->date);
//	header->date = 10;
//	printf("%d\n", header->date);
//	header->next = 20;
//	printf("%d\n", header->date);
//	return header;
//}
//int main()
//{
//	Linklist linklist = creat_linklist();
//	return 0;
//}

//#include<stdlib.h>
//
//typedef struct node {
//    int date;
//    struct node* next;
//}Node, * Linklist;
//
//Linklist creat_linklist()//头节点
//{
//    Linklist header = (Linklist)calloc(4, sizeof(Node));
//    if (NULL == header)
//    {
//        perror("error create header");
//        return NULL;
//    }
//    header->next = NULL;
//    return header;
//}
//
//void creat_node1(Linklist linklist, int num)//尾结点
//{
//    Linklist dingwei = linklist;
//    while (NULL != dingwei->next)//判断指针域是否为空
//    {
//        dingwei = dingwei->next;
//    }
//    Linklist tailpointer = (Linklist)malloc(sizeof(Node));
//    if (NULL == tailpointer)
//    {
//        printf("定位失败\n");
//        return;
//    }
//    dingwei->next = tailpointer;
//    tailpointer->next = NULL;
//    tailpointer->date = num;
//    return;
//}
//
////插入任意位置
//void creat_node2(Linklist linklist, int num, int score)
//{
//    Linklist test = linklist;
//    while (NULL != test->next)
//    {
//        if (num == test->next->date)
//        {
//            break;
//        }
//        test = test->next;
//    }
//    if (NULL == test->next)
//    {
//        printf("插入失败,未找到数字:%d\n", num);
//        return;
//    }
//    Linklist current1 = (Linklist)malloc(sizeof(Node));
//    if (NULL == current1)
//    {
//        printf("内存分配失败\n");
//        return;
//    }
//    current1->date = score;
//    current1->next = NULL;
//    current1->next = test->next;
//    test->next = current1;
//    return;
//}

//删除尾结点
//void prmove_Node(Linklist linklist)
//{
//    Linklist test1 = linklist;
//    if (NULL == test1->next)
//    {
//        printf("链表为空\n");
//        return;
//    }
//    while (NULL != test1->next)
//    {
//        if (NULL == test1->next->next)
//        {
//            break;
//        }
//        test1 = test1->next;
//    }
//    free(test1->next);//存放下一个节点的地址和数据
//    test1->next = NULL;
//}


//void pop_linklist(Linklist linklist)
//{
//    while (NULL != linklist->next) {
//        if (NULL == linklist->next->next)
//            break;
//        linklist = linklist->next;
//    }
//    // 空链表
//    if (NULL == linklist->next) {
//        printf("空链表：\n");
//        return;
//    }
//
//    Node* prmv_node = linklist->next;
//
//    linklist->next = NULL;
//    free(prmv_node);
//    prmv_node = NULL;
//    return;
//}
//
//
//
//void Print_linklist(Linklist linklist)//打印
//{
//    //打印链表
////从有数据的首元结点开始打印
//    Linklist current = linklist->next;
//    printf("循环打印：");
//    while (NULL != current)
//    {
//        printf("%d ", current->date);
//        current = current->next;
//    }
//
//}
//
//void Release_linklist(Linklist linklist)//释放链表
//{
//    //释放链表
//    Linklist current = linklist;
//    while (NULL != current)//地址
//    {
//        Linklist temp = current;
//        current = current->next;
//        free(temp);//释放当前节点（地址 内存）
//        temp = NULL;
//    }
//}
//
//int main()
//{
//    Linklist linklist = creat_linklist();
//    Linklist linklist1 = (Linklist)malloc(sizeof(Node));
//    if (NULL == linklist1)
//    {
//        perror("error of linklist1");
//        return 1;
//    }
//    linklist->next = linklist1;
//    linklist1->next = NULL;
//    linklist1->date = 66;
//    printf("linklist1:%d\n", linklist1->date);
//    //在链表尾部添加一个新数据节点
//    creat_node1(linklist, 98);
//    creat_node1(linklist, 96);
//    creat_node1(linklist, 95);
//    printf("tailpointer_new:%d\n", linklist->next->next->date);
//    creat_node2(linklist, 98, 100);
//    creat_node2(linklist, 100, 55);
//    creat_node2(linklist, 66, 77);
//    creat_node2(linklist, 6000, 77);
//    Print_linklist(linklist);//打印
//    pop_linklist(linklist);
//    //prmove_Node(linklist);//删除尾结点
//    Print_linklist(linklist);//打印
//    Release_linklist(linklist);//释放
//    return 0;
//}

#include <stdio.h>
#include <stdlib.h>

typedef struct list {
    int data;
    struct list* next;
}List, * Linklist;

Linklist creat_Node()
{
    Linklist header = (Linklist)malloc(sizeof(List));
    if (NULL == header)
    {
        printf("malloc error\n");
        return NULL;
    }
    header->next = NULL;
    return header;
}

void creat_node(Linklist header, int num)
{
    if (NULL == header)
        return;
    Linklist p = header;
    while (NULL != p->next)
    {
        p = p->next;
    }
    Linklist a = (Linklist)malloc(sizeof(List));
    p->next = a;
    a->data = num;
    a->next = NULL;
}

void Print_linklist(Linklist header)
{
    if (NULL == header)
        return;
    Linklist p = header->next;
    while (NULL != p)
    {
        printf("%d ", p->data);
        p = p->next;
    }
}

void Release_linklist(Linklist header)
{
    if (NULL == header)
        return;
    while (NULL != header)
    {
        Linklist p = header;
        header = header->next;
        free(p);
        p = NULL;
    }
}

int main()
{
    Linklist header = creat_Node();
    int i = 0;
    for (i = 0; i < 10; i++)
    {
        creat_node(header, i);
    }
    Print_linklist(header);
    Release_linklist(header);
    return 0;
}
