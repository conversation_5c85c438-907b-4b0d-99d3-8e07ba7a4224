using System;
using System.Text;
using System.Windows.Forms;
using Script.Methods;
using System.Runtime.InteropServices;
using System.IO;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

 using VisionMaster;
 using VisionMaster.Image;

/************************************
Shell Module default code: using .NET Framwwork 4.6.1
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
	//执行次数计数
    int processCount ;  

    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;

       
    }
    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>

    public bool Process()
    {
        //You can add your codes here, for realizing your desired function
		//每次执行将进入该函数，此处添加所需的逻辑流程处理
		float upperlimit;
		float lowerlimit;
		float number;
		string[] parts = limit.Split(';');  // 先按照；分割字符串
		string[] lines = input.Split(new string[] { ";" }, StringSplitOptions.None);
		string[] i = new string[lines.Length - 5];
		Array.Copy(lines, 5, i, 0, i.Length);
		string sourceImagePath = image_path + @"\" + image_name ;
		string currentDate = DateTime.Now.ToString("yyyyMMdd");
		string targetImagePath = @"E:\缓存\相机1\" + currentDate + @"\" + Path.GetFileName(sourceImagePath);
		Directory.CreateDirectory(Path.GetDirectoryName(targetImagePath));
		File.Copy(sourceImagePath, targetImagePath, true);
		//将remainingLines数组中的每一行依次存储到i数组对应的索引位置
		for (int j = 0; j < i.Length-1; j++)
		{
			int index = i[j].IndexOf('V');
	    	string name = i[j].Substring(0, index);
			string area = i[j].Substring(index + 1);			
			float.TryParse(area, out number);		
			foreach (string part in parts)
        {
            if (!string.IsNullOrEmpty(part))
            {
                string[] item = part.Split(',');  // 再按照，分割每个部分获取项目名和数值	
                if (item.Length == 2 && string.Equals(item[0].Trim(), name, StringComparison.OrdinalIgnoreCase))
                {
                    if (float.TryParse(item[1].Trim(), out upperlimit))
                    {
                        if (number < upperlimit && inOK == 1)
                        {	string anotherFolderPath = @"E:\保存原图\良品\" + name +  @"\" + currentDate + @"\";
							Directory.CreateDirectory(anotherFolderPath);
							string copySavePath = anotherFolderPath + Path.GetFileName(targetImagePath);
							File.Copy(targetImagePath, copySavePath, true);
							}
                        if(number >　upperlimit && inNG == 1)
                        	{	
                        		string anotherFolderPath = @"E:\保存原图\不良品\" + name  + @"\" + currentDate + @"\";
								Directory.CreateDirectory(anotherFolderPath);
								string copySavePath = anotherFolderPath + Path.GetFileName(targetImagePath);
								File.Copy(targetImagePath, copySavePath, true);	

                        		}
                }}
            }
        }
		

		}
		File.Delete(sourceImagePath);
		File.Delete(targetImagePath);
		
		imgOut = imgIn;
		
        return true;
    }
        public static void Execute()
    {
        try
        {
            // 获取输出图像变量（outImage 是你在流程中定义的图像变量名）
            IVisionImage outImage = (IVisionImage)Context.Variables["outImage"].GetValue();

            // 图像路径
            string imagePath = @"C:\Images\test.png";

            // 使用 VisionMaster 提供的图像读取方法
            IVisionImage loadedImage = VisionImageFactory.Load(imagePath);

            // 将加载后的图像赋值给输出变量
            Context.Variables["outImage"].SetValue(loadedImage);
        }
        catch (Exception ex)
        {
            Context.LogMessage("图像加载失败：" + ex.Message);
        }
        
        
        try
        {
            // 获取输出图像变量（outImage 是你在流程中定义的图像变量名）
            IVisionImage outImage = (IVisionImage)Context.Variables["outImage"].GetValue();

            // 图像路径
            string imagePath = @"C:\Images\test.png";

            // 使用 VisionMaster 提供的图像读取方法
            IVisionImage loadedImage = VisionImageFactory.Load(imagePath);

            // 将加载后的图像赋值给输出变量
            Context.Variables["outImage"].SetValue(loadedImage);
        }
        catch (Exception ex)
        {
            Context.LogMessage("图像加载失败：" + ex.Message);
        }
        
        
    }


        
        
}

