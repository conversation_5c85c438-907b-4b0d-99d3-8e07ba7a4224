#include "MysqlManager.h"

void ContactUtil::saveContact() const {
    ofstream file("friend.dat", ios::binary);
    if (!file) {
        cout << "文件打开失败" << endl;
        return;
    }
    for (auto pair : ContactUtil::contacts) {
        file.write((char*)&pair.second, sizeof(Friend));
    }
    file.close();
    dirtyFlag = false;
}

void ContactUtil::loadContacts()
{
    ifstream file("friend.dat", ios::binary);
    if (!file)
    {
        cout << "文件打开失败" << endl;
        return;
    }
    Friend temp;
    while (file.read((char*)&temp, sizeof(temp)))
    {
        contacts[temp.phone] = temp;
    }
    file.close();
    dirtyFlag = false;
}

void ContactUtil::addContact()
{
    Friend fri;
    cout << "请输入好友姓名：" << endl;
    cin >> fri.name;
    cout << "请输入好友电话：" << endl;
    cin >> fri.phone;
    for (auto i : contacts)
    {
        if (strcmp(i.second.phone, fri.phone) == 0)
        {
            cout << "该电话号码已存在" << endl;
            return;
        }
    }
    cout << "请输入好友地址：" << endl;
    cin >> fri.address;
    contacts[fri.phone] = fri; //
    dirtyFlag = true;
}

void ContactUtil::deleteContact()
{
    char* phone = new char[16];
    cout << "请输入要删除的联系人电话：" << endl;
    cin >> phone;
    auto it = contacts.find(phone);
    if (it == contacts.end())
    {
        cout << "该联系人不存在" << endl;
        return;
    }
    contacts.erase(it);
    delete[] phone;
    dirtyFlag = true;
}
void ContactUtil::searchContact() const
{
    char* phone = new char[16];
    cout << "请输入要查找的联系人电话：" << endl;
    cin >> phone;
    std::map<string, Friend>::const_iterator it = contacts.find(phone); // 只能通过键值查找
    if (it == contacts.end())
    {
        cout << "该联系人不存在" << endl;
        return;
    }
    const Friend& fri = it->second;
    cout << "姓名:" << fri.name << endl;
    cout << "地址:" << fri.address << endl;
    cout << "电话:" << fri.phone << endl;
    delete[] phone;
}

void ContactUtil::updateContact()
{
    char* phone = new char[16];
    cout << "请输入要修改的联系人电话：" << endl;
    cin >> phone;
    auto it = contacts.find(phone);
    if (it == contacts.end())
    {
        cout << "该联系人不存在" << endl;
        return;
    }
    cout << "请输入新的姓名:" << endl;
    cin >> it->second.name;
    cout << "请输入新的地址:" << endl;
    cin >> it->second.address;
    delete[] phone;
    dirtyFlag = true;
}

void menu() {
    ContactUtil util;
    util.contacts = util.contacts;
    cout << "好友通信名单" << endl;
    for (auto pair : util.contacts) {
        cout << "手机号:" << pair.first << "---姓名:" << pair.second.name << ",地址:" << pair.second.address << endl;
    }
    int choice;
    do {
        cout << "1.添加好友信息" << endl;
        cout << "2.删除好友信息" << endl;
        cout << "3.修改好友信息" << endl;
        cout << "4.查找好友信息" << endl;
        cout << "5.退出" << endl;
        cout << "6.保存" << endl;
        cout << "7.加载" << endl;
        cin >> choice;
        switch (choice) {
        case 1:
            util.addContact();
            break;
        case 2:
            util.deleteContact();
            break;
        case 3:
            util.updateContact();
            break;
        case 4:
            util.searchContact();
            break;
        case 5:
            if (dirtyFlag)
            {
                cout << "是否保存修改？Y/N" << endl;
                char c;
                cin >> c;
                if (c == 'y' || c == 'Y')
                {
                    util.saveContact();
                }
                else
                {
                    cout << "已放弃保存" << endl;
                }
            }
            break;
        case 6:
            util.saveContact();
            break;
        case 7:
            util.loadContacts();
            break;
        default:
            cout << "输入错误" << endl;
            break;
        }
    } while (choice != 5);
    return;
}

int main()
{
    menu();
    return 0;
}