# LogRich 批处理文件使用说明

本项目提供了4个批处理文件，方便用户快速启动和测试LogRich应用程序。

## 📁 批处理文件列表

### 1. 启动主程序.bat
**用途**: 启动LogRich主应用程序
**功能**:
- 自动检查.NET 6.0运行时环境
- 编译主程序代码
- 启动图形界面应用程序
- 显示程序功能说明

**使用方法**: 双击运行即可

### 2. 启动测试客户端.bat
**用途**: 启动测试客户端发送模拟数据
**功能**:
- 检查主程序是否已启动 (端口12380监听状态)
- 编译测试客户端代码
- 发送6条测试数据到主程序
- 显示测试数据说明

**使用方法**: 
1. 先运行"启动主程序.bat"
2. 再运行此批处理文件

### 3. 快速测试.bat
**用途**: 一键完成完整测试流程
**功能**:
- 自动编译主程序和测试客户端
- 后台启动主程序
- 自动运行测试客户端
- 显示生成的CSV文件内容
- 完整的测试验证流程

**使用方法**: 双击运行，适合首次使用或快速验证功能

### 4. 停止所有程序.bat
**用途**: 清理所有运行中的LogRich相关进程
**功能**:
- 查找并停止LogRich.exe进程
- 查找并停止相关dotnet.exe进程
- 检查端口12380占用状态
- 提供手动确认选项

**使用方法**: 在需要清理进程时运行

## 🚀 推荐使用流程

### 首次使用
1. 运行 `快速测试.bat` - 完整验证所有功能
2. 检查生成的CSV文件确认数据正确性

### 日常使用
1. 运行 `启动主程序.bat` - 启动主应用程序
2. 运行 `启动测试客户端.bat` - 发送测试数据 (可选)
3. 运行 `停止所有程序.bat` - 清理进程 (结束时)

### 开发调试
1. 运行 `启动主程序.bat` - 启动并查看详细信息
2. 手动使用外部Socket客户端发送数据
3. 运行 `停止所有程序.bat` - 清理环境

## ⚠️ 注意事项

1. **系统要求**: 需要安装.NET 6.0运行时环境
2. **防火墙**: 确保端口12380未被防火墙阻止
3. **权限**: 以管理员权限运行可避免权限问题
4. **进程管理**: 使用停止程序批处理文件避免进程残留
5. **文件权限**: 确保对项目目录有写入权限

## 📊 输出文件说明

### Data目录
- **位置**: 项目根目录下的Data文件夹
- **文件格式**: `PRec_{产品型号}_{日期}.csv`
- **内容**: 检测信息记录，包含流水号、检测时间、各相机判定分类

### 示例文件
```
Data/
├── PRec_0523A_20250618.csv    # 产品型号0523A的数据
├── PRec_H1213_20250618.csv    # 产品型号H1213的数据
└── PRec_3168P_20250618.csv    # 产品型号3168P的数据
```

## 🔧 故障排除

### 常见问题

1. **"未找到.NET 6.0运行时"**
   - 解决: 从 https://dotnet.microsoft.com/download 下载并安装.NET 6.0

2. **"端口12380被占用"**
   - 解决: 运行"停止所有程序.bat"或重启计算机

3. **"编译失败"**
   - 解决: 检查代码文件是否完整，重新下载项目文件

4. **"连接失败"**
   - 解决: 确保主程序已启动并显示"正在监听端口 12380"

5. **"权限被拒绝"**
   - 解决: 以管理员权限运行批处理文件

### 日志查看
- 主程序的日志显示在监控标签页的文本框中
- 测试客户端的输出显示在命令行窗口中
- CSV文件可用Excel或记事本打开查看

## 📞 技术支持

如遇到问题，请检查：
1. .NET 6.0是否正确安装
2. 防火墙和杀毒软件设置
3. 项目文件是否完整
4. 系统权限设置

更多详细信息请参考 `README.md` 和 `测试说明.md` 文件。 