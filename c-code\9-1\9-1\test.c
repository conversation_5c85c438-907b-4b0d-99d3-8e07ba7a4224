#include <stdio.h>





 //int main()
 //{
 //    FILE *fp=fopen("8.31.txt","r");
 //    if(NULL==fp)
 //    {
 //        perror("error");
 //        return 1;
 //    }
 //    char ch[100]={};
 //    fgets(ch, sizeof(ch), fp);
 //    printf("%s\n",ch);
 //    fclose(fp);
 //    return 0;
 //}

// int main()
// {
//     FILE *fp=fopen("8.31-destination.txt","w");
//     if(NULL==fp)
//     {
//         perror("error");
//         return 1;
//     }
//     fputs("123456\n",fp);
//     fputs("hello world",fp);
//     fclose(fp);
//     return 0;
// }

// int main()
// {
//     FILE *fp;
//     int num;
//     fp=fopen("test0.1.txt","w");
//     if(NULL==fp)
//     {
//         perror("error");
//         return 1;
//     }
//     printf("请输入一个整数：");
//     scanf("%d",&num);
//     fprintf(fp,"%d",num);
//     fclose(fp);

//     fp=fopen("test0.1.txt","r");
//     if(NULL==fp)
//     {
//         perror("error");
//         return 1;
//     }
//     fscanf(fp,"%d",&num);
//     printf("从文件中读取的整数是%d\n",num);
//     fclose(fp);
//     return 0;
// }

// //格式化方式读写
// typedef struct student
// {
//     char name[20];
//     int age;
//     float score;
// }Stu;

// int main()
// {
//     ////写
//     // Stu stu={"wangwu" ,20,98.5};
//     // FILE *fp=fopen("8.31-test1.txt","w");
//     // if(NULL==fp)
//     // {
//     //     perror("error");
//     //     return 1;
//     // }
//     // fprintf(fp,"%s %d %.2f",stu.name,stu.age,stu.score);
//     // fclose(fp);

//     //读
//     Stu stu={0};
//     FILE *fp=fopen("8.31-test1.txt","r");
//     if(NULL==fp)
//     {
//         perror("error");
//         return 1;
//     }
//     fscanf(fp,"%s %d %g\n",stu.name,&stu.age,&stu.score);
//     printf("%s %d %g\n",stu.name,stu.age,stu.score);
//     fscanf(fp,"%s %d %g\n",stu.name,&stu.age,&stu.score);
//     printf("%s %d %g\n",stu.name,stu.age,stu.score);
//     return 0;
// }

//typedef struct student
//{
//    char name[20];
//    int age;
//    float score;
//} Stu;

// // 数据块写入方式
// int main()
// {
//     // 学生数据写入磁盘
//     Stu student[2] = {
//         {"wangwu", 20, 98.5},
//         {"zhangsan", 21, 98.5}};
//     FILE *fp = fopen("8.31-test2.txt", "w");
//     if (NULL == fp)
//     {
//         perror("error");
//         return 1;
//     }
//     fwrite(student,sizeof(Stu),sizeof(student)/sizeof(Stu),fp);
//     fclose(fp);


//     return 0;
// }

// int main()
// {
//     //学生数据写入磁盘
//     Stu student[2] = {0};
//     FILE *fp = fopen("8.31-test2.txt", "r");
//     if (NULL == fp)
//     {
//         perror("error");
//         return 1;
//     }
//     fread(student, sizeof(Stu), 2, fp);
//     printf("%s %d %g\n", student[0].name, student[0].age, student[0].score);
//     printf("%s %d %g\n", student[1].name, student[1].age, student[1].score);
//     fclose(fp);
//     return 0;
// }