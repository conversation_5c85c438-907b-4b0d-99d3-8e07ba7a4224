#define _CRT_SECURE_NO_WARNINGS



//#include <iostream>
//#include<string>
//using namespace std;
//class Date {
//protected:
//	int year;
//	int month;
//	int day;
//public:
//	Date():year(2010),month(6),day(17){}
//	Date(int year,int month,int day):year{year},month{month},day{day}{}
//	string display()const
//	{
//		return to_string(year) + "/" + to_string(month) + "/" + to_string(day);
//	}
//	
//};
//ostream& operator<< (ostream& cout, Date& s1)
//{
//	cout << s1.display()<<endl;
//	return cout;
//}
//
//int main()
//{
//	Date d1, d3;
//	Date d2{ 2024,9,30 };
//	cout << d1 << d2;
//	return 0;
//}


#include<iostream>
#include<string>
using namespace std;
int temperature = 28;
class appliances {
public:
    virtual void changedTemp() = 0;
    virtual ~appliances() {}
};
class AirColor : public appliances {
public:
    void changedTemp()override {
        cout << "温度降低..." << endl;
        temperature -= 1;
    }
};
class AirHeater : public appliances {
public:
    void changedTemp()override {
        cout << "温度升高..." << endl;
        temperature += 1;
    }
};
class AirCondition :public AirColor, public AirHeater {
public:
    //void changedTemp()override {}
    void tempUp()
    {
        AirHeater::changedTemp();        
    }
    void tempDown()
    {
        AirColor::changedTemp();    
    }
    void showTemperature()
    {
        cout << "Current temperature: " << temperature << "°C" << endl;
    }
};

int main() {
    AirCondition ac;
    ac.showTemperature();
    ac.tempUp(); 
    ac.showTemperature();
    ac.tempDown(); 
    ac.showTemperature();
    ac.tempDown();
    ac.showTemperature(); 
    return 0;
}