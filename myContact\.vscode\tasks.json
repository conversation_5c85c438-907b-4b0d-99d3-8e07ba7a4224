{
    "version": "2.0.0",
    "tasks": 
    [
        {
            "label": "C/C++: g++.exe build active file",
            "type": "process",
            "command": "F:/soft/mingw64/mingw64/bin/g++.exe",  // 改成你自己的路径
            "args":
            [
                "-fdiagnostics-color=always",
                "-g", 
                "${file}",
                "-L",
                "F:\\soft\\MySQL\\MySQL Server 5.7\\Install\\lib", // mysql 库文件 
                "-I",
                "F:\\soft\\MySQL\\MySQL Server 5.7\\Install\\include", // mysql头文件 
                "-llibmysql", // 导入那个库
                "-o",
                "${fileDirname}\\${fileBasenameNoExtension}.exe"
            ],
            "options": 
            {
                "cwd": "F:/soft/mingw64/mingw64/bin/"  // 改成你自己的路径
            },
            "group": "build",
            "detail": "compiler: F:/soft/mingw64/mingw64/bin/g++.exe",  // 改成你自己的路径 
            "problemMatcher":  ["$gcc"]
        }
    ]
}