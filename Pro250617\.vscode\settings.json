{"workbench.colorTheme": "Visual Studio 2019 Dark", "editor.suggest.snippetsPreventQuickSuggestions": false, "code-runner.runInTerminal": true, "files.autoSave": "after<PERSON>elay", "code-runner.executorMap": {"javascript": "node", "java": "cd $dir && javac $fileName && java $fileNameWithoutExt", "c": "cd $dir && gcc $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt", "cpp": "cd $dir && g++ $fileName -o $fileNameWithoutExt -I F:/soft/opencv/opencv/build/include -L F:/soft/opencv/opencv/build/x64/MinGW/bin  -l libopencv_world455 -l opencv_videoio_ffmpeg455_64 && $dir$fileNameWithoutExt", "objective-c": "cd $dir && gcc -framework Cocoa $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt", "php": "php", "python": "python -u", "perl": "perl", "perl6": "perl6", "ruby": "ruby", "go": "go run", "lua": "lua", "groovy": "groovy", "powershell": "powershell -ExecutionPolicy ByPass -File", "bat": "cmd /c", "shellscript": "bash", "fsharp": "fsi", "csharp": "scriptcs", "vbscript": "cscript //Nologo", "typescript": "ts-node", "coffeescript": "coffee", "scala": "scala", "swift": "swift", "julia": "julia", "crystal": "crystal", "ocaml": "ocaml", "r": "Rscript", "applescript": "osascript", "clojure": "lein exec", "haxe": "haxe --cwd $dirWithoutTrailingSlash --run $fileNameWithoutExt", "rust": "cd $dir && rustc $fileName && $dir$fileNameWithoutExt", "racket": "racket", "scheme": "csi -script", "ahk": "autohotkey", "autoit": "autoit3", "dart": "dart", "pascal": "cd $dir && fpc $fileName && $dir$fileNameWithoutExt", "d": "cd $dir && dmd $fileName && $dir$fileNameWithoutExt", "haskell": "<PERSON><PERSON><PERSON>", "nim": "nim compile --verbosity:0 --hints:off --run", "lisp": "sbcl --script", "kit": "kitc --run", "v": "v run", "sass": "sass --style expanded", "scss": "scss --style expanded", "less": "cd $dir && lessc $fileName $fileNameWithoutExt.css", "FortranFreeForm": "cd $dir && gfortran $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt", "fortran-modern": "cd $dir && gfortran $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt", "fortran_fixed-form": "cd $dir && gfortran $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt", "fortran": "cd $dir && gfortran $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt"}, "window.zoomLevel": 1, "files.associations": {"iostream": "cpp", "ostream": "cpp"}, "C_Cpp_Runner.msvcBatchPath": "", "C_Cpp_Runner.cCompilerPath": "gcc", "C_Cpp_Runner.cppCompilerPath": "g++", "C_Cpp_Runner.debuggerPath": "gdb", "C_Cpp_Runner.cStandard": "", "C_Cpp_Runner.cppStandard": "", "C_Cpp_Runner.useMsvc": false, "C_Cpp_Runner.warnings": ["-Wall", "-Wextra", "-Wpedantic", "-W<PERSON>dow", "-Wformat=2", "-Wcast-align", "-Wconversion", "-Wsign-conversion", "-W<PERSON><PERSON>-dereference"], "C_Cpp_Runner.msvcWarnings": ["/W4", "/permissive-", "/w14242", "/w14287", "/w14296", "/w14311", "/w14826", "/w44062", "/w44242", "/w14905", "/w14906", "/w14263", "/w44265", "/w14928"], "C_Cpp_Runner.enableWarnings": true, "C_Cpp_Runner.warningsAsError": false, "C_Cpp_Runner.compilerArgs": [], "C_Cpp_Runner.linkerArgs": [], "C_Cpp_Runner.includePaths": [], "C_Cpp_Runner.includeSearch": ["*", "**/*"], "C_Cpp_Runner.excludeSearch": ["**/build", "**/build/**", "**/.*", "**/.*/**", "**/.vscode", "**/.vscode/**"], "C_Cpp_Runner.useAddressSanitizer": false, "C_Cpp_Runner.useUndefinedSanitizer": false, "C_Cpp_Runner.useLeakSanitizer": false, "C_Cpp_Runner.showCompilationTime": false, "C_Cpp_Runner.useLinkTimeOptimization": false, "C_Cpp_Runner.msvcSecureNoWarnings": false}