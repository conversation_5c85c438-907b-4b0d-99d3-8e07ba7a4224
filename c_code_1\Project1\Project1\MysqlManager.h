#define _CRT_SECURE_NO_WARNINGS

#pragma once    //防止重复包含
#include <iostream>
//#include <mysql.h>
#include <map>
#include <fstream>
#include <cstring>
#include <windows.h>
#include <limits> // for std::numeric_limits
using namespace std;

struct Friend {
    int id;
    char name[20]; // char二进制存储 
    char phone[20];
    char address[100];
};

// class MysqlManager {
// private:
//     MYSQL* m_Mysql = new MYSQL(); // Mysql连接句柄
//     MYSQL_RES* m_SelectResult;    // 用于存储mysql查询结果，用指针指向存储的查询结果
// public:
//     const char* m_host;           // 主机地址   127.0.0.1
//     const char* m_username;       // 账号名   root
//     const char* m_password;       // 账号密码
//     const char* m_database;       // 连接的数据库名称
//     unsigned int m_port;          // Mysql端口号，默认3306
//     const char* m_unix_socket;    // unix连接标识
//     unsigned long m_clientflag;   // 客户端连接标志

//     bool init();                  // 初始化数据库连接
//     void freeConnect();           // 数据库连接的释放
//     bool runSql(const char *sql); // 执行对mysql数据库的增删改操作(insert、delete、update三种sql语句)
//     bool selectData(const char *sql); // 执行对mysql数据库的查询操作(select语句)，SQL语句在c和c++中只能作为字符串处理，用数据库引擎的字符串处理函数处理
//     map<string, Friend> loadContact(); // 从数据库加载好友信息到通讯录容器
//     void saveContact(const map<string, Friend>& contact); // 保存好友信息到数据库

//     void menu(map<string, Friend>& contact, MysqlManager& mysql); // 菜单函数  
//     void addContact(map<string, Friend>& contact);
//     void deleteContact();
//     void updateContact();
//     void searchContact();
//     static map<string, Friend> contacts;
// };

class ContactUtil {
private:
public:
    static map<string, Friend> contacts;
    void saveContact() const;
    void loadContacts();
    void addContact();
    void deleteContact();
    void updateContact();
    void searchContact() const;
};
bool dirtyFlag = false;
void pressEnter();
void menu();