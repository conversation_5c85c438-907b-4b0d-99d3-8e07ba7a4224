using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Script.Methods;
using Newtonsoft.Json;

/* 简化版本用于调试 - 添加详细日志记录 */

/************************************
Shell Module default code: using .NET Framework 4.6.1
*************************************/
public partial class UserScript : ScriptMethods, IProcessMethods
{
    int processCount;
    private string debugLogPath = @"D:\logs\script_debug.log";

    public void Init()
    {
        try
        {
            processCount = 0;
            WriteDebugLog("=== Init() 方法开始执行 ===");
            WriteDebugLog("processCount 初始化为: " + processCount);
            WriteDebugLog("=== Init() 方法执行完成 ===");
        }
        catch (Exception ex)
        {
            WriteDebugLog("Init() 方法异常: " + ex.Message);
            WriteDebugLog("异常堆栈: " + ex.StackTrace);
        }
    }

    public bool Process()
    {
        try
        {
            WriteDebugLog("=== Process() 方法开始执行 ===");
            WriteDebugLog("processCount: " + processCount);
            processCount++;

            // 检查关键变量是否存在
            WriteDebugLog("检查 exImg 变量...");
            string exImgValue = "";
            try
            {
                exImgValue = exImg ?? "null";
                WriteDebugLog("exImg 值: '" + exImgValue + "'");
            }
            catch (Exception ex)
            {
                WriteDebugLog("获取 exImg 失败: " + ex.Message);
                return false;
            }

            WriteDebugLog("检查其他变量...");
            try
            {
                string modelnoValue = modelno ?? "null";
                string itemsSvValue = itemsSv ?? "null";
                string resInfoValue = resInfo ?? "null";

                WriteDebugLog("modelno: '" + modelnoValue + "'");
                WriteDebugLog("itemsSv: '" + itemsSvValue + "'");
                WriteDebugLog("resInfo: '" + resInfoValue + "'");
            }
            catch (Exception ex)
            {
                WriteDebugLog("获取其他变量失败: " + ex.Message);
            }

            if(exImgValue != "" && exImgValue != "null")
            {
                WriteDebugLog("exImg 不为空，调用 Evaluate 方法");
                judgeMnt = SimpleEvaluate(modelno, itemsSv, resInfo);
                WriteDebugLog("Evaluate 返回结果: " + judgeMnt);
            }
            else
            {
                WriteDebugLog("exImg 为空或null，设置 judgeMnt = -9");
                judgeMnt = -9;
            }

            WriteDebugLog("=== Process() 方法执行完成，返回 true ===");
            return true;
        }
        catch (Exception ex)
        {
            WriteDebugLog("Process() 方法异常: " + ex.Message);
            WriteDebugLog("异常堆栈: " + ex.StackTrace);
            return false;
        }
    }

    private void WriteDebugLog(string message)
    {
        try
        {
            string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            string logMessage = timestamp + " : " + message;

            // 确保目录存在
            string directory = Path.GetDirectoryName(debugLogPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            File.AppendAllText(debugLogPath, logMessage + Environment.NewLine, Encoding.UTF8);
        }
        catch
        {
            // 忽略日志写入错误，避免影响主流程
        }
    }

    // 简化版本的评估方法，用于调试
    private int SimpleEvaluate(string modelNo, string inspecItems, string resultInfo)
    {
        try
        {
            WriteDebugLog("=== SimpleEvaluate 开始执行 ===");
            WriteDebugLog("参数 modelNo: '" + (modelNo ?? "null") + "'");
            WriteDebugLog("参数 inspecItems: '" + (inspecItems ?? "null") + "'");
            WriteDebugLog("参数 resultInfo: '" + (resultInfo ?? "null") + "'");

            // 基本参数检查
            if (string.IsNullOrEmpty(modelNo))
            {
                WriteDebugLog("modelNo 为空，返回 -1");
                return -1;
            }

            if (string.IsNullOrEmpty(inspecItems))
            {
                WriteDebugLog("inspecItems 为空，返回 -2");
                return -2;
            }

            if (string.IsNullOrEmpty(resultInfo))
            {
                WriteDebugLog("resultInfo 为空，返回 -3");
                return -3;
            }

            // 检查配置文件路径
            string jsonPath = @"D:\Config Parameters\品番管理\" + modelNo + @"\检测项目和限度.json";
            WriteDebugLog("配置文件路径: " + jsonPath);

            if (!File.Exists(jsonPath))
            {
                WriteDebugLog("配置文件不存在，返回 -4");
                return -4;
            }

            WriteDebugLog("配置文件存在，尝试读取...");

            // 简单的文件读取测试
            try
            {
                string jsonContent = File.ReadAllText(jsonPath);
                WriteDebugLog("配置文件读取成功，内容长度: " + jsonContent.Length);

                // 简单的JSON解析测试
                if (jsonContent.Contains("[") && jsonContent.Contains("]"))
                {
                    WriteDebugLog("JSON格式基本正确");
                }
                else
                {
                    WriteDebugLog("JSON格式可能有问题");
                    return -5;
                }
            }
            catch (Exception ex)
            {
                WriteDebugLog("读取配置文件失败: " + ex.Message);
                return -6;
            }

            // 简单的结果信息解析测试
            WriteDebugLog("解析结果信息...");
            string[] resultParts = resultInfo.Split(';');
            WriteDebugLog("结果信息分段数量: " + resultParts.Length);

            // 查找Items标记
            int itemsIndex = -1;
            for (int i = 0; i < resultParts.Length; i++)
            {
                if (resultParts[i].Trim().StartsWith("Items-"))
                {
                    itemsIndex = i;
                    WriteDebugLog("找到Items标记在位置: " + i);
                    break;
                }
            }

            if (itemsIndex == -1)
            {
                WriteDebugLog("未找到Items标记，返回 -7");
                return -7;
            }

            WriteDebugLog("=== SimpleEvaluate 执行完成，返回 0 (测试成功) ===");
            return 0; // 测试成功
        }
        catch (Exception ex)
        {
            WriteDebugLog("SimpleEvaluate 异常: " + ex.Message);
            WriteDebugLog("异常堆栈: " + ex.StackTrace);
            return -99;
        }
    }
}
