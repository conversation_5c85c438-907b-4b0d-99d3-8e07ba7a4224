using System;
using System.IO;
using System.Text;
using Script.Methods; // VisionMaster的IProcessMethods接口所需的命名空间

/// <summary>
/// VisionMaster的主脚本类，必须实现IProcessMethods接口
/// </summary>
public partial class UserScript : IProcessMethods
{
    private int _lastFrameNumber = 0; // 跟踪Process调用之间的上次帧号



    /// <summary>
    /// 脚本初始化时调用一次
    /// </summary>
    public void Init()
    {
        // 这是使用日志记录器的示例
        // 可以在此处放置初始化代码
        LogWriter.WriteLog(@"D:\Log4Script", string.Format("vm{0}.txt", UserScript.DeviceId), "脚本已初始化。");
    }

    /// <summary>
    /// 在处理周期中重复调用
    /// </summary>
    public static string DeviceId = "C1"; // 设备标识，在本文件内统一赋值

    public bool Process()
    {
        int currentFrameNumber = fnNew;  //外部新的帧

        string logContent;
        if (currentFrameNumber != _lastFrameNumber + 1)
        {
            logContent = string.Format("帧号不连续提示！上次帧：{0}，当前帧：{1}", _lastFrameNumber, currentFrameNumber);
        }
        else
        {
            logContent = string.Format("正常处理，当前帧：{0}，_lastFrameNumber帧：{1}", currentFrameNumber,_lastFrameNumber);
        }

        // 更新输出变量和上次帧号
        fnUpdate = currentFrameNumber;
        _lastFrameNumber = currentFrameNumber;
	
        
        string fileName = string.Format("vm{0}_{1}.txt", UserScript.DeviceId, DateTime.Now.ToString("yyyyMMdd"));
        LogWriter.WriteLog(@"D:\Log4Script", fileName, logContent);
        return true;
    }

    /// <summary>
    /// 用于将消息记录到文件的独立工具类
    /// </summary>
    public static class LogWriter
    {
        private static readonly object _lock = new object(); // 用于线程同步的锁对象

        /// <summary>
        /// 将消息写入指定的日志文件
        /// </summary>
        /// <param name="directoryPath">日志文件存储的目录</param>
        /// <param name="fileName">日志文件的名称</param>
        /// <param name="content">要写入的消息内容</param>
        public static void WriteLog(string directoryPath, string fileName, string content)
        {
            try
            {
                // 确保目录存在
                if (!Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }

                // 组合路径和文件名
                string filePath = Path.Combine(directoryPath, fileName);

                // 获取带毫秒的当前时间
                string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");

                // 使用string.Format格式化日志条目，以兼容C# 5.0
                string logEntry = string.Format("{0} | {1}{2}", timestamp, content, Environment.NewLine);

                // 使用锁防止多线程文件访问冲突
                lock (_lock)
                {
                    // 将日志条目追加到文件
                    File.AppendAllText(filePath, logEntry, Encoding.UTF8);
                }
            }
            catch (Exception ex)
            {
                // 处理潜在异常是良好的实践
                Console.WriteLine(string.Format("写入日志文件时出错：{0}", ex.Message));
            }
        }
    }
}
