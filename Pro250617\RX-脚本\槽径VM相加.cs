using System;
using System.Text;
using System.Windows.Forms;
using Script.Methods;
/************************************
Shell Module default code: using .NET Framwwork 4.6.1
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
	//执行次数计数
    int processCount ;  

    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;
       
    }

    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>
    public bool Process()
    {
        //解析输入数据
        string[] inputParts = in0.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
        double[] numbers;
        try
        {
            numbers = Array.ConvertAll(inputParts, double.Parse);
        }
        catch (Exception)
        {
            return false; // 解析失败返回false
        }

        int length = numbers.Length;
        if (length == 0 || length % 2 != 0)
        {
            return false; // 数组长度必须为偶数且不为空
        }

        int halfLength = length / 2;
        double[] sumArray = new double[halfLength];

        // 前一半与后一半对应位置相加
        for (int i = 0; i < halfLength; i++)
        {
            sumArray[i] = numbers[i] + numbers[i + halfLength];
        }

        // 创建结果数组并复制前一半到后一半
        double[] resultArray = new double[length];
        sumArray.CopyTo(resultArray, 0);
        sumArray.CopyTo(resultArray, halfLength);

        // 转换结果为字符串格式（假设输出到out0变量）
        GroDia = string.Join(",", resultArray);

        return true;
    }
}
                            