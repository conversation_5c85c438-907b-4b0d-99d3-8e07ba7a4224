using System;
using System.IO;
using System.Text;
using Script.Methods; // Required namespace for VisionMaster's IProcessMethods interface

/// <summary>
/// Main script class for VisionMaster. It must implement IProcessMethods.
/// </summary>
public class UserScript : IProcessMethods
{
    /// <summary>
    /// This method is called once when the script is initialized.
    /// </summary>
    public void Init()
    {
        // This is an example of how to use the logger.
        // You can place your initialization code here.
        LogWriter.WriteLog(@"D:\Log4Script", "ScriptEvents.txt", "Script initialized.");
    }

    /// <summary>
    /// This method is called repeatedly in the processing cycle.
    /// </summary>
 

    public bool Process()
    {
        // 从外部获取当前帧号（需根据实际接口实现）
        int currentFrameNumber = 0; 
        int _lastFrameNumber = 0;  // 保存上次获取的外部帧号

        string logContent;
        if (currentFrameNumber != _lastFrameNumber + 1)
        {
            logContent = string.Format("帧号不连续提示！上次帧：{0}，当前帧：{1}", _lastFrameNumber, currentFrameNumber);
        }
        else
        {
            logContent = string.Format("正常处理，当前帧：{0}", currentFrameNumber);
        }

        // 更新上次帧号为当前值，供下次判断使用
        _lastFrameNumber = currentFrameNumber;

        LogWriter.WriteLog(@"D:\Log4Script", "ScriptEvents.txt", logContent);
        return true;
    }

    // 示例：获取外部输入的方法（需根据实际接口调整）

    /// <summary>
    /// A self-contained utility class for logging messages to a file.
    /// </summary>
    public static class LogWriter
    {
        private static readonly object _lock = new object();

        /// <summary>
        /// Writes a message to a specified log file.
        /// </summary>
        /// <param name="directoryPath">The directory where the log file will be stored.</param>
        /// <param name="fileName">The name of the log file.</param>
        /// <param name="content">The message content to write.</param>
        public static void WriteLog(string directoryPath, string fileName, string content)
        {
            try
            {
                // Ensure the directory exists.
                if (!Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }

                // Combine path and filename.
                string filePath = Path.Combine(directoryPath, fileName);

                // Get the current time with milliseconds.
                string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");

                // Format the log entry using string.Format for C# 5.0 compatibility.
                string logEntry = string.Format("{0} | {1}{2}", timestamp, content, Environment.NewLine);

                // Use a lock to prevent file access conflicts from multiple threads.
                lock (_lock)
                {
                    // Append the log entry to the file.
                    File.AppendAllText(filePath, logEntry, Encoding.UTF8);
                }
            }
            catch (Exception ex)
            {
                // It's good practice to handle potential exceptions.
                Console.WriteLine(string.Format("Error writing to log file: {0}", ex.Message));
            }
        }
    }
}
