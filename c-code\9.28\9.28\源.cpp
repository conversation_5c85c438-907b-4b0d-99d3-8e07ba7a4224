#define _CRT_SECURE_NO_WARNINGS
#include <iostream>
#include <string>
#include <cstring>
using namespace std;
//class Person
//{
//private:
//    string name;
//    int age;
//
//public:
//    Person() : name(), age() {}
//    Person(string name, int age) : name(name), age(age) {}
//    string getName() const{
//        return name;
//    }
//    int getAge() const{
//        return age;
//    }
//    void setName(string name) {
//        this->name = name;
//    }
//
//};
//class Stu : public Person
//{
//private:
//    float score;
//    int* test;
//    
//public:
//    Stu() : score(), test(nullptr) {}
//    Stu(string name, int age, float score, int n) : Person(name, age), score(score) {
//        test = new int[n];
//        for (int i = 0; i <sizeof(test)-1; i++) {
//            test[i] = i;
//        }
//    }
//    ~Stu() { delete test; }
//    void show() {
//        cout << "name:" << getName() << endl;
//        cout << "age:" << getAge() << endl;
//        cout << "score:" << score << endl;
//        for (int i = 0; i < sizeof(test)-1; i++) {
//            cout << test[i] ;
//        }
//        cout << endl;
//    }
//    void swapString(Stu &s1 ,Stu& s2)
//    {
//        string temp=s1.getName();
//        s1.setName(s2.getName());
//        s2.setName(temp);
//    }
//
//};
//int main()
//{
//    Stu s1{ "zhangsan", 20, 98.5,3 };
//    Stu s2{ "lisi", 18, 98.5,3 };
//    s1.show();
//    s1.swapString(s1,s2);
//    s1.show();
//    s2.show();
//
//    return 0;
//}

//
//class calculator {
//	public:
//		int count;
//		int num;
//		char* name;
//		int* n;
//		static int total;
//	public:
//		calculator() :count{}, num{}, name{}, n{}{total++; }
//		calculator(int count, int num, const char* name,int n) :count{ count }, num{ num }{
//			this->name = new char[strlen(name) + 1];
//			strcpy(this->name, name);
//			this->n = new int[n];
//			for (int i = 0; i < n; i++)
//			{
//				this->n[i] = i;
//			}
//			total++;
//		}
//		~calculator() {
//			delete[] n; }
//		static void test()
//		{
//			total ++;
//		}
//		calculator operator+(calculator &add1)
//		{
//			calculator temp;
//			size_t totalLength = strlen(name) + strlen(add1.name);
//			 temp.name = new char[totalLength + 1];
//			strcat(temp.name, name);
//			strcat(temp.name, add1.name);
//			return temp;
//		}
//		void print()
//		{
//			cout << "count:"<<count<<",num:"<<num<<",name:"<<name<<",total:"<<total<<endl;
//			for (int i = 0; i < sizeof(n)-1; i++)
//			{
//				cout << n[i];
//			}
//			cout << endl;
//		}
//		const char* getName()
//		{
//			return name;
//		}
//};
//int calculator::total = 0;
//ostream& operator<<(ostream& cout, calculator &a)
//{
//	cout << "count:" << a.count << ",num:" << a.num << ",name" << a.name  << ",total:" << a.total;
//	return cout;
//}
//
//
//int main()
//{
//	calculator c1 = { 10,15,"wangwu",5 };
//	calculator c2 = {10,15,"666",5};
//	c1.print();
//	calculator temp=c1 + c2;
//	cout<<temp.getName()<<endl;
//	cout << c1 << endl;
//	return 0;
//}


//class Animal {
//public:
//	virtual void woof() //加virtual使函数woof成为动态多态，在dowoof函数内晚绑定，编译阶段确定函数地址，子类里函数重写，重定义，基类同名函数隐藏
//						//不加virtual，派生类函数重写，静态多态，先分配地址，运行阶段确定函数地址，子类函数重写
//	{
//		cout << "叫" << endl;
//	}
//};
//class Dog :public Animal {
//public:
//	void woof() {
//		cout << "狗叫" << endl;
//	}
//};
//class Add :public Animal {
//public:
//	void woof() {
//		cout << "猫叫" << endl;
//	}
//};
//void dowoof(Animal& s1)
//{
//	s1.woof();
//}
//int main() {
//	Dog dog;
//	dowoof(dog);
//	return 0;
//}

//class calculator {
//public:
//	int num1;
//	int num2;
//	calculator() :num1{}, num2{}{}
//	calculator(int n1, int n2) :num1{n1}, num2{ n2 }{}
//	virtual int Cal()
//	{
//		return 0;
//	}
//};
//class add :public calculator {
//public:
//	int Cal() {
//		return num1 + num2;
//	}
//};
//int main()
//{
//	add ad;
//	ad.num1 = 10;
//	ad.num2 = 15;
//	cout<<ad.Cal() << endl;
//}

class Base {
public:
	virtual void test() = 0;
	void test2()
	{
		test();
	}
	//virtual ~Base();
};
class Son :public Base {
public:
	void test()
	{
		cout << "1" << endl;
	}
	
};
int main()
{
	Son s1;
	Base* s = new Son;
	s->test();
	s->test2();
}

